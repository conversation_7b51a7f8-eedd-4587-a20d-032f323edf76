import mylib as mylib
import os
from langchain_ollama import OllamaEmbeddings, OllamaLLM
import chromadb

llm_model = "llama3.2"
embedding = mylib.ChromaDBEmbeddingFunction(
    OllamaEmbeddings(
        model=llm_model,
        base_url="http://localhost:11434"  # Adjust the base URL as per your Ollama server configuration
    )
)

chroma_client = chromadb.PersistentClient(path=os.path.join(os.getcwd(), "chroma_db"))
collection_name = "rag_collection_demo_1"
collection = chroma_client.get_or_create_collection(
    name=collection_name,
    metadata={"description": "A collection for RAG with Ollama - Demo1"},
    embedding_function=embedding  # Use the custom embedding function
)

def query(query_text):
    """
    Process a query using the RAG pipeline

    Args:
        query_text (str): The query text to process

    Returns:
        str: The response from the LLM
    """
    response = mylib.rag_pipeline(query_text, collection, llm_model)
    return response

# For testing purposes when running this file directly
if __name__ == "__main__":
    test_query = input("Mi a kérdés? -> ")
    response = query(test_query)
    print("######## Response from LLM ########\n", response)